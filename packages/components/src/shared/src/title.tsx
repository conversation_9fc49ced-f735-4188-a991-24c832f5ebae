import { type BlobType, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, HTMLAttributes, ReactNode, Ref } from 'react'

export const createTitleVariants = cva([], {
  variants: {
    size: {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-md',
      lg: 'text-lg',
      xl: 'text-xl',
      '2xl': 'text-2xl',
      '3xl': 'text-3xl',
    },
    thickness: {
      thin: 'font-thin',
      extralight: 'font-extralight',
      light: 'font-light',
      normal: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
      bold: 'font-bold',
      extrabold: 'font-extrabold',
      black: 'font-black',
    },
    decoration: {
      none: 'no-underline',
      underline: 'underline',
      lineThrough: 'line-through',
    },
    color: {
      white: 'text-white',
      black: 'text-black',
    },
  },
  defaultVariants: {
    size: 'md',
    thickness: 'normal',
    decoration: 'none',
  },

  presets: {
    h1: {
      size: '3xl',
      thickness: 'bold',
    },
    h2: {
      size: '2xl',
      thickness: 'bold',
    },
    h3: {
      size: 'xl',
      thickness: 'bold',
    },
    h4: {
      size: 'lg',
      thickness: 'semibold',
    },
    h5: {
      size: 'md',
      thickness: 'semibold',
    },
    h6: {
      size: 'sm',
      thickness: 'semibold',
    },
  },
})

export type TitleProps = ({
  as?: `h${1 | 2 | 3 | 4 | 5 | 6}`
  ref?: Ref<HTMLHeadingElement>
} & HTMLAttributes<HTMLHeadingElement>) &
  VariantProps<typeof createTitleVariants> & { children?: ReactNode; className?: string }

export const Title: FC<TitleProps> = ({ as = 'h6', children, preset = as, className, ...rest }): ReactNode => {
  const El = as as BlobType
  const styles = createTitleVariants({
    preset,
    className,
    ...rest,
  })

  return (
    <El className={styles} {...rest}>
      {children}
    </El>
  )
}
