'use client'
import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC } from 'react'
import { Text } from './text'

const FIRST_ROW_IMAGE_COUNT = 3
const SECOND_ROW_START_INDEX = 3
const TOTAL_ITEM = 5

const categoryCardVariants = cva(['rounded-xl', 'px-8 md:px-12', 'py-6 md:py-8', 'font-display', 'w-full'], {
  variants: {
    color: {
      primary: ['bg-light-green'],
      secondary: ['bg-light-purple'],
    },
    size: {
      lg: 'max-w-300',
      sm: 'max-w-150 sm:max-w-200',
    },
  },
  defaultVariants: {
    color: 'primary',
    size: 'lg',
  },
})

const buttonVariants = cva(['rounded-max', 'flex items-center justify-center text-white'], {
  variants: {
    color: {
      primary: ['bg-green text-black hover:bg-black hover:text-white hover:cursor-pointer transform duration-300'],
      secondary: ['bg-purple hover:bg-black hover:cursor-pointer transform duration-300'],
    },
    size: {
      lg: 'w-64 sm:w-70 md:w-80 h-15 sm:h-16 md:h-17.5',
      sm: 'w-50 sm:w-55 md:w-60 h-14 sm:h-13.5 md:h-15 mt-3 sm:mt-4 md:mt-5',
    },
  },
  defaultVariants: {
    color: 'primary',
    size: 'lg',
  },
})

const containerVariants = cva([], {
  variants: {
    size: {
      lg: 'flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6 sm:mb-8 md:mb-10 mt-2',
      sm: 'flex flex-col items-start justify-between mb-6 sm:mb-8 md:mb-10 mt-2',
    },
  },
  defaultVariants: {
    size: 'lg',
  },
})

const imageVariants = cva(['w-full', 'rounded-xl', 'bg-cover', 'bg-top', 'bg-no-repeat'], {
  variants: {
    size: {
      sm: 'h-40',
      lg: 'h-70',
    },
  },
  defaultVariants: {
    size: 'lg',
  },
})

interface CategoryCardProps extends VariantProps<typeof categoryCardVariants> {
  title: string
  imageUrl: string[]
  buttonText: string
  className?: string
}

const CategoryCard: FC<React.HTMLAttributes<HTMLDivElement> & CategoryCardProps> = ({
  title,
  imageUrl,
  buttonText,
  color,
  size,
  className,
  ...props
}) => {
  return (
    <article className={categoryCardVariants({ color, size })} {...props}>
      <div className={containerVariants({ size })}>
        <Text size='2xl' thickness='medium'>
          {title}
        </Text>
        <div className={buttonVariants({ color, size })}>
          <Text size='md' thickness='medium'>
            {buttonText}
          </Text>
        </div>
      </div>
      <div className={clsx('space-y-4 sm:space-y-6 md:space-y-8')}>
        <div className='grid grid-cols-3 gap-2 sm:gap-3 md:gap-5'>
          {imageUrl.slice(0, FIRST_ROW_IMAGE_COUNT).map(url => (
            <div key={url} className={imageVariants({ size })} style={{ backgroundImage: `url(${url})` }} />
          ))}
        </div>
        {imageUrl.length > FIRST_ROW_IMAGE_COUNT && (
          <div className='grid grid-cols-2 gap-2 sm:gap-3 md:gap-5'>
            {imageUrl.slice(SECOND_ROW_START_INDEX, TOTAL_ITEM).map(url => (
              <div key={url} className={imageVariants({ size })} style={{ backgroundImage: `url(${url})` }} />
            ))}
          </div>
        )}
      </div>
    </article>
  )
}

export { CategoryCard, categoryCardVariants, type CategoryCardProps }
