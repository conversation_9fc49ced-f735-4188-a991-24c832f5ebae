import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import { type FC, memo, type ReactNode } from 'react'
import { Text } from './text'

const cardVariants = cva(['rounded-2xl', 'flex flex-col', 'font-display', 'group', 'hover:cursor-pointer'], {
  variants: {
    variant: {
      primary: 'bg-white',
      secondary: 'bg-light-spring',
      tertiary: 'bg-light-purple',
      quaternary: 'bg-transparent',
    },
    size: {
      sm: 'w-150',
      lg: 'w-230',
    },
  },
  defaultVariants: {
    variant: 'primary',
    size: 'sm',
  },
})

type BaseProps = VariantProps<typeof cardVariants> & {
  image: string
  title: string
  description: string
  link?: string
  badge?: ReactNode
}

type CardProps =
  | (BaseProps & { type: 'primary'; tag?: string; hoverComponent?: never; showTexts?: never })
  | (BaseProps & { type: 'secondary'; tag?: never; hoverComponent?: ReactNode; showTexts?: boolean })

const Card: FC<CardProps> = memo(
  ({ type, image, title, description, tag, badge, variant, size, hoverComponent, showTexts = true }) => {
    return (
      <>
        {type === 'primary' && (
          <div className={clsx(cardVariants({ variant, size }))}>
            <div className={clsx('overflow-hidden', 'rounded-t-2xl')}>
              <div
                className={clsx(
                  'h-125',
                  'rounded-t-2xl',
                  'bg-cover',
                  'bg-center',
                  'transition-transform',
                  'duration-500',
                  'group-hover:scale-110',
                )}
                style={{ backgroundImage: `url(${image})` }}
              />
            </div>
            <div className={clsx('pt-7 pr-8 pb-9 pl-8', 'flex flex-col')}>
              <div>
                {tag && (
                  <div className={clsx('mb-2', 'flex items-center justify-between')}>
                    <Text as='span' size='xs' className={clsx('text-dark-gray')}>
                      {tag}
                    </Text>
                  </div>
                )}
                <div className={clsx('flex items-center justify-between', 'mb-2')}>
                  <Text as='span' thickness='bold' size='sm'>
                    {title}
                  </Text>
                  {badge && <div>{badge}</div>}
                </div>
                <Text as='p' thickness='normal' size='xs' className={clsx('text-dark-gray', 'text-left', 'w-100')}>
                  {description}
                </Text>
              </div>
            </div>
          </div>
        )}
        {type === 'secondary' && (
          <div className={clsx(cardVariants({ variant, size }))}>
            <div
              className={clsx(
                'overflow-hidden',
                'rounded-2xl',
                showTexts ? 'p-4' : '',
                variant === 'quaternary' ? 'bg-white' : '',
              )}>
              <div className={clsx('relative', 'rounded-2xl', 'overflow-hidden')}>
                <div
                  className={clsx('h-125', 'rounded-2xl', 'bg-cover', 'bg-center')}
                  style={{ backgroundImage: `url(${image})` }}
                />
                <div
                  className={clsx(
                    'absolute inset-0',
                    'bg-black',
                    'rounded-2xl',
                    'transition-opacity',
                    'duration-800',
                    'opacity-0',
                    'group-hover:opacity-10',
                  )}
                />
                {hoverComponent && (
                  <div
                    className={clsx(
                      'absolute inset-0',
                      'flex',
                      'items-end',
                      'justify-center',
                      'pb-4',
                      'opacity-0',
                      'group-hover:opacity-100',
                      'transition-opacity',
                      'duration-800',
                      'pointer-events-none',
                    )}>
                    <div className={clsx('bg-white', 'rounded-lg', 'px-4 py-4')}>{hoverComponent}</div>
                  </div>
                )}
              </div>
            </div>
            {showTexts && (
              <div className={clsx('pt-4 pr-20 pb-4 pl-13', 'flex flex-col')}>
                <div>
                  <div className={clsx('flex items-center justify-start gap-3', 'mb-2')}>
                    <Text as='span' thickness='bold' size='sm'>
                      {title}
                    </Text>
                    {badge && <div className={clsx('flex items-center', 'h-full')}>{badge}</div>}
                  </div>
                  <Text as='p' thickness='normal' size='xs' className={clsx('text-dark-gray', 'text-left', 'w-100')}>
                    {description}
                  </Text>
                </div>
              </div>
            )}
          </div>
        )}
      </>
    )
  },
)

export { Card, type CardProps }
