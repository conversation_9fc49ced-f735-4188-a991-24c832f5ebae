{"dependencies": {"arktype": "^2.1.20"}, "devDependencies": {"@venture-vibe/icons": "workspace:*", "@venture-vibe/utils": "workspace:*", "@ozaco/cli": "^0.0.14", "@types/react": "^19.1.9", "motion": "^12.23.12", "react": "^19.1.1", "typescript": "^5.9.2", "@legendapp/state": "^3.0.0-beta.31"}, "exports": {"./shared": {"default": "./dist/shared.js", "source": "./src/shared/index.ts", "types": "./dist/shared.d.ts"}}, "tsx-exports": ["shared"], "files": ["dist"], "name": "@venture-vibe/components", "peerDependencies": {"@venture-vibe/icons": "workspace:*", "@venture-vibe/utils": "workspace:*", "motion": ">= 12.23.12", "react": ">= 19.1.1", "typescript": ">= 5.9.2", "@legendapp/state": ">= 3.0.0-beta.31"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "private": true, "type": "module", "version": "0.0.0"}