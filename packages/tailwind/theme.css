@theme {
  --\*: initial;

  --spacing: 0.125rem;

  --breakpoint-xs: 26.25rem;
  --breakpoint-sm: 40rem;
  --breakpoint-md: 48rem;
  --breakpoint-lg: 64rem;
  --breakpoint-xl: 80rem;
  --breakpoint-2xl: 96rem;

  --blur-xs: 4px;
  --blur-sm: 8px;
  --blur-md: 12px;
  --blur-lg: 16px;
  --blur-xl: 24px;
  --blur-2xl: 40px;
  --blur-3xl: 64px;

  --aspect-video: 16 / 9;

  --font-display: 'DM Sans', sans-serif;

  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  --text-2xs: 0.625rem;
  --text-2xs--line-height: 0.825rem;
  --text-xs: 0.75rem;
  --text-xs--line-height: 1rem;
  --text-sm: 0.875rem;
  --text-sm--line-height: 1.25rem;
  --text-md: 1rem;
  --text-md--line-height: 1.5rem;
  --text-lg: 1.125rem;
  --text-lg--line-height: 1.75rem;
  --text-xl: 1.25rem;
  --text-xl--line-height: 1.75rem;
  --text-2xl: 1.5rem;
  --text-2xl--line-height: 2rem;
  --text-3xl: 1.875rem;
  --text-3xl--line-height: 2.25rem;

  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-4xl: 2rem;
  --radius-full: 100%;
  --radius-max: 2.097rem; /* 33554400px max css value */

  --color-transparent: transparent;
  --color-white: #fff;
  --color-dark-gray: #373934;
  --color-gray: #9ca3af;
  --color-light-gray: #e8ebe2;
  --color-black: #000;
  --color-spring-bud: #adff00;
  --color-spring-stroke: #dde8c6;
  --color-dark-spring: #7cae0e;
  --color-light-spring: #f1f9e7;
  --color-bg: #f6f6f6;
  --color-platinum-stroke: #e8ebe2;
  --color-light-purple: #f1eafc;
  --color-purple-stroke: #e4dcf0;
  --color-purple: #7521fb;
  --color-dark-gray: #373934;
  --color-light-orange: #fff2eb;
  --color-orange-stroke: #f4e2d9;
  --color-orange: #ff7c33;
  --color-bright-orange: #ffa401;
  --color-bright-blue: #018af4;
  --color-danger: #ff2244;
  --color-spanish-gray: #939393;

  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}
