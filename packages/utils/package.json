{"dependencies": {"class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "3.3.1"}, "devDependencies": {"@ozaco/cli": "^0.0.14", "type-fest": "^4.41.0", "typescript": "^5.9.2", "react": "^19.1.1", "@legendapp/state": "^3.0.0-beta.31", "arktype": "^2.1.20"}, "exports": {".": {"default": "./dist/index.js", "source": "./src/index.ts", "types": "./dist/index.d.ts"}}, "files": ["dist"], "name": "@venture-vibe/utils", "peerDependencies": {"typescript": ">= 5.9.2", "react": ">= 19.1.1", "@legendapp/state": ">= 3.0.0-beta.31", "arktype": ">= 2.1.20"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "private": true, "type": "module", "version": "0.0.0"}