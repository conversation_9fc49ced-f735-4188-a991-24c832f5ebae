{"devDependencies": {"@biomejs/biome": "2.1.4", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/prompt-cli": "^19.8.1", "@types/bun": "^1.2.19", "husky": "^9.1.7", "typescript": "^5.9.2"}, "name": "venture-vibe", "private": "true", "scripts": {"commit": "commit", "prepare": "husky"}, "trustedDependencies": ["@biomejs/biome", "@tailwindcss/oxide", "bufferutil", "es5-ext", "sharp", "utf-8-validate"], "type": "module", "version": "0.0.0", "workspaces": ["{packages,plugins,apps,tools,experiments}/**"]}