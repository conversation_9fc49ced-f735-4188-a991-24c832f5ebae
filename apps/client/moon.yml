language: "typescript"
type: "library"
platform: "bun"

tasks:
  dev:
    local: true
    command: "next dev --turbopack"
    deps: ["icons:dev", "components:dev", "utils:dev"]
    env:
      NODE_ENV: development
    inputs:
      - "src/**/*"
    outputs:
      - ".next/**"
  build:
    script: "next build"
    toolchain: 'bun'
    deps: ["icons:build", "components:build","utils:build"]
    inputs:
      - "src/**/*"
    outputs:
      - ".next/**"
  start:
    local: true
    deps: ["client:build"]
    command: "next start"
    inputs:
      - ".next/**"
