import { Card, Text } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

export default function Home() {
  return (
    <div
      className={clsx(
        'flex min-h-screen w-screen flex-col items-center justify-center',
        'bg-bg',
        'space-y-8 p-4',
        'font-display',
      )}>
      <div className={clsx('flex flex-row items-center justify-center', 'w-full max-w-7xl', 'space-x-8')}>
        <Card
          type='primary'
          variant='primary'
          size='sm'
          image='https://framerusercontent.com/images/FcU6rnATaKaz50pAhYUpNKl1UEU.jpg?scale-down-to=512'
          title='SAP SaaS'
          description='Added Oct 24, 2024 in Testimonial.'
          tag='Antaresrvish'
          badge={
            <Text
              as='span'
              size='xs'
              thickness='thin'
              className={clsx('bg-bg', 'text-neutral-700', 'rounded-max', 'px-4', 'py-2')}>
              $50
            </Text>
          }
        />
        <Card
          type='primary'
          variant='secondary'
          size='lg'
          image='https://framerusercontent.com/images/FcU6rnATaKaz50pAhYUpNKl1UEU.jpg?scale-down-to=512'
          title='SAP SaaS'
          description='Added Oct 24, 2024 in Testimonial.'
          tag='Antaresrvish'
          badge={
            <Text
              as='span'
              size='xs'
              thickness='thin'
              className={clsx('bg-spring-stroke', 'text-neutral-700', 'rounded-max', 'px-4', 'py-2')}>
              $50
            </Text>
          }
        />
      </div>
      <div className={clsx('flex flex-row items-center justify-center', 'w-full max-w-7xl', 'space-x-8')}>
        <Card
          type='secondary'
          showTexts={true}
          hoverComponent={
            <div className={clsx('space-x-4')}>
              <button type='button' className={clsx('bg-purple', 'text-white', 'rounded-lg', 'px-4', 'py-2')}>
                Preview
              </button>
              <button type='button' className={clsx('bg-white', 'text-black', 'rounded-lg', 'px-4', 'py-2')}>
                Copy
              </button>
            </div>
          }
          variant='tertiary'
          size='sm'
          image='https://framerusercontent.com/images/FcU6rnATaKaz50pAhYUpNKl1UEU.jpg?scale-down-to=512'
          title='SAP SaaS'
          description='Added Oct 24, 2024 in Testimonial.'
          badge={
            <Text
              as='span'
              size='xs'
              thickness='thin'
              className={clsx('bg-spring-bud', 'text-dark-gray', 'rounded-max', 'px-4', 'py-0')}>
              Pro
            </Text>
          }
        />
        <Card
          type='secondary'
          showTexts={true}
          hoverComponent={
            <div className={clsx('space-x-4')}>
              <button type='button' className={clsx('bg-purple', 'text-white', 'rounded-lg', 'px-4', 'py-2')}>
                Preview
              </button>
              <button type='button' className={clsx('bg-white', 'text-black', 'rounded-lg', 'px-4', 'py-2')}>
                Copy
              </button>
            </div>
          }
          variant='quaternary'
          size='lg'
          image='https://framerusercontent.com/images/FcU6rnATaKaz50pAhYUpNKl1UEU.jpg?scale-down-to=512'
          title='SAP SaaS'
          description='Added Oct 24, 2024 in Testimonial.'
          badge={
            <Text
              as='span'
              size='xs'
              thickness='thin'
              className={clsx('bg-spring-bud', 'text-dark-gray', 'rounded-max', 'px-4', 'py-0')}>
              Pro
            </Text>
          }
        />
        <Card
          type='secondary'
          showTexts={false}
          hoverComponent={
            <div className={clsx('space-x-4')}>
              <button type='button' className={clsx('bg-purple', 'text-white', 'rounded-lg', 'px-4', 'py-2')}>
                Preview
              </button>
              <button type='button' className={clsx('bg-white', 'text-black', 'rounded-lg', 'px-4', 'py-2')}>
                Copy
              </button>
            </div>
          }
          variant='tertiary'
          size='sm'
          image='https://framerusercontent.com/images/FcU6rnATaKaz50pAhYUpNKl1UEU.jpg?scale-down-to=512'
          title='SAP SaaS'
          description='Added Oct 24, 2024 in Testimonial.'
          badge={
            <Text
              as='span'
              size='xs'
              thickness='thin'
              className={clsx('bg-spring-bud', 'text-dark-gray', 'rounded-max', 'px-4', 'py-0')}>
              Pro
            </Text>
          }
        />
      </div>
    </div>
  )
}
