{"[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "biome.enabled": true, "editor.codeActionsOnSave": {"source.action.useSortedKeys.biome": "explicit", "source.fixAll.biome": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "eslint.enable": false, "files.associations": {"*.css": "css"}, "css.validate": false, "javascript.preferences.importModuleSpecifier": "shortest", "prettier.enable": false, "typescript.disableAutomaticTypeAcquisition": false, "typescript.preferences.importModuleSpecifier": "project-relative", "typescript.tsdk": "node_modules/typescript/lib", "tailwindCSS.classFunctions": ["cva", "cx"], "deno.enablePaths": ["apps/server/functions"], "deno.unstable": ["bare-node-builtins", "byonm", "sloppy-imports", "unsafe-proto", "webgpu", "broadcast-channel", "worker-options", "cron", "kv", "ffi", "fs", "http", "net"]}